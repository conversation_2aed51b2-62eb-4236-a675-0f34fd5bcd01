import React, { useState, useEffect, useCallback, useContext } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import api from "../../api";
import { LocationContext } from "../../contexts";
import PageContent from "../../ui/PageContent";
import Button from "../../ui/Button";
import Modal from "../../ui/Modal";
import Card from "../../ui/Card";
import TextInput from "../../ui/form/TextInput";
import { SearchTable, useSearchTableQueryState } from "../../ui/SearchTable";
import { SearchResult } from "../../types";
import {
  PlusIcon,
  CalendarIcon,
  MapPinIcon,
  ClockIcon,
  DollarSignIcon,
  EditIcon,
  TrashIcon,
} from "../../ui/icons";
import Spinner from "../../ui/Spinner";
import { toast, Toaster } from "react-hot-toast";

interface Event {
  id: number;
  event_id: string;
  event_name: string;
  category?: string[];
  start_time?: string;
  timezone?: string;
  host?: string;
  starting_price?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  image?: string;
  url?: string;
  source: string;
  created_at: string;
  updated_at: string;
}

interface EventFormData {
  event_name: string;
  category: string[];
  start_time: string;
  timezone: string;
  host: string;
  starting_price: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  image: string;
  url: string;
}

const Events: React.FC = () => {
  const { t } = useTranslation();
  const { locationId } = useParams<{ locationId: string }>();
  const navigate = useNavigate();
  const [location] = useContext(LocationContext);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Helper function to get default date (today + 1 hour)
  const getDefaultStartTime = () => {
    const now = new Date();
    now.setHours(now.getHours() + 1, 0, 0, 0); // Set to next hour, clear minutes/seconds
    return now.toISOString().slice(0, 16); // Format for datetime-local input
  };

  // Form state
  const [formData, setFormData] = useState<EventFormData>({
    event_name: "",
    category: [],
    start_time: getDefaultStartTime(),
    timezone: "",
    host: "",
    starting_price: "",
    address: "",
    city: "",
    state: "",
    postal_code: "",
    image: "",
    url: "",
  });

  // Search table state
  const state = useSearchTableQueryState<Event>(
    useCallback(
      async (params): Promise<SearchResult<Event> | null> => {
        if (!locationId) {
          return {
            results: [],
            total: 0,
            nextCursor: "",
            limit: 25,
          } as SearchResult<Event>;
        }

        try {
          const page = typeof params.page === "number" ? params.page : 1;
          const response = await api.events.getLocationEvents(
            parseInt(locationId),
            {
              page,
              limit: params.limit || 25,
              search: params.q || undefined,
              future_only: false,
            }
          );

          return {
            results: response.events || [],
            total: response.total_count || 0,
            nextCursor: "",
            limit: params.limit || 25,
          } as SearchResult<Event>;
        } catch (error) {
          console.error("Error loading events:", error);
          return {
            results: [],
            total: 0,
            nextCursor: "",
            limit: 25,
          } as SearchResult<Event>;
        }
      },
      [locationId]
    )
  );

  // Handle create event
  const handleCreateEvent = async () => {
    if (!locationId) return;

    // Validate start time is in the future
    if (formData.start_time) {
      const startDate = new Date(formData.start_time);
      const now = new Date();
      if (startDate <= now) {
        toast.error("Event start time must be in the future");
        return;
      }
    }

    setSubmitting(true);
    try {
      await api.events.createEvent(parseInt(locationId), {
        ...formData,
        category: formData.category.length > 0 ? formData.category : undefined,
      });

      toast.success("Event created successfully!");
      setShowCreateModal(false);
      resetForm();
      await state.reload();
    } catch (error) {
      console.error("Error creating event:", error);
      toast.error("Failed to create event. Please try again.");
    }
    setSubmitting(false);
  };

  // Handle edit event
  const handleEditEvent = async () => {
    if (!locationId || !editingEvent) return;

    // Validate start time is in the future (if provided)
    if (formData.start_time) {
      const startDate = new Date(formData.start_time);
      const now = new Date();
      if (startDate <= now) {
        toast.error("Event start time must be in the future");
        return;
      }
    }

    setSubmitting(true);
    try {
      await api.events.updateEvent(
        parseInt(locationId),
        editingEvent.event_id,
        {
          ...formData,
          category:
            formData.category.length > 0 ? formData.category : undefined,
        }
      );

      toast.success("Event updated successfully!");
      setShowEditModal(false);
      setEditingEvent(null);
      resetForm();
      await state.reload();
    } catch (error) {
      console.error("Error updating event:", error);
      toast.error("Failed to update event. Please try again.");
    }
    setSubmitting(false);
  };

  // Handle delete event
  const handleDeleteEvent = async (event: Event) => {
    if (!locationId) return;

    if (
      window.confirm(
        t("confirm_delete_event", "Are you sure you want to delete this event?")
      )
    ) {
      try {
        await api.events.deleteEvent(parseInt(locationId), event.event_id);
        toast.success("Event deleted successfully!");
        await state.reload();
      } catch (error) {
        console.error("Error deleting event:", error);
        toast.error("Failed to delete event. Please try again.");
      }
    }
  };

  // Open edit modal
  const openEditModal = (event: Event) => {
    setEditingEvent(event);
    setFormData({
      event_name: event.event_name,
      category: event.category || [],
      start_time: event.start_time || "",
      timezone: event.timezone || "",
      host: event.host || "",
      starting_price: event.starting_price || "",
      address: event.address || "",
      city: event.city || "",
      state: event.state || "",
      postal_code: event.postal_code || "",
      image: event.image || "",
      url: event.url || "",
    });
    setShowEditModal(true);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      event_name: "",
      category: [],
      start_time: getDefaultStartTime(),
      timezone: "",
      host: "",
      starting_price: "",
      address: "",
      city: "",
      state: "",
      postal_code: "",
      image: "",
      url: "",
    });
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "TBD";
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        weekday: "short",
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return dateString;
    }
  };

  // Handle row click to navigate to event detail (if needed)
  const handleRowClick = (event: Event) => {
    // For now, just open edit modal
    openEditModal(event);
  };

  return (
    <>
      <Toaster position="top-center" />
      <PageContent
        title={t("events", "Events")}
        desc={t("manage_location_events", "Manage events for your location")}
        actions={
          <Button icon={<PlusIcon />} onClick={() => setShowCreateModal(true)}>
            {t("create_event", "Create Event")}
          </Button>
        }
      >
        <SearchTable
          {...state}
          results={state.results as SearchResult<Event>}
          columns={[
            {
              key: "event_name",
              title: t("event_name", "Event Name"),
              sortable: true,
              cell: ({ item }) => (
                <div className="flex flex-col">
                  <span className="font-medium">{item.event_name}</span>
                  {item.host && (
                    <span className="text-sm text-gray-500">
                      by {item.host}
                    </span>
                  )}
                </div>
              ),
            },
            {
              key: "start_time",
              title: t("date_time", "Date & Time"),
              sortable: true,
              cell: ({ item }) => (
                <div className="flex items-center">
                  <ClockIcon className="w-4 h-4 mr-2 text-gray-400" />
                  <span>{formatDate(item.start_time)}</span>
                </div>
              ),
            },
            {
              key: "location",
              title: t("location", "Location"),
              sortable: false,
              cell: ({ item }) => {
                const locationParts = [
                  item.address,
                  item.city,
                  item.state,
                ].filter(Boolean);
                return locationParts.length > 0 ? (
                  <div className="flex items-center">
                    <MapPinIcon className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="text-sm">{locationParts.join(", ")}</span>
                  </div>
                ) : (
                  "-"
                );
              },
            },
            {
              key: "starting_price",
              title: t("price", "Price"),
              sortable: true,
              cell: ({ item }) =>
                item.starting_price ? (
                  <div className="flex items-center">
                    <DollarSignIcon className="w-4 h-4 mr-2 text-gray-400" />
                    <span>{item.starting_price}</span>
                  </div>
                ) : (
                  "-"
                ),
            },
            {
              key: "category",
              title: t("categories", "Categories"),
              sortable: false,
              cell: ({ item }) =>
                item.category && item.category.length > 0 ? (
                  <div className="flex flex-wrap gap-1">
                    {item.category.slice(0, 2).map((cat, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {cat}
                      </span>
                    ))}
                    {item.category.length > 2 && (
                      <span className="text-xs text-gray-500">
                        +{item.category.length - 2} more
                      </span>
                    )}
                  </div>
                ) : (
                  "-"
                ),
            },
            {
              key: "actions",
              title: t("actions", "Actions"),
              sortable: false,
              cell: ({ item }) => (
                <div className="flex items-center gap-2">
                  <Button
                    size="small"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      openEditModal(item);
                    }}
                  >
                    <div className="w-4 h-4">
                      <EditIcon />
                    </div>
                  </Button>
                  <Button
                    size="small"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteEvent(item);
                    }}
                    className="text-red-600 hover:text-red-700"
                  >
                    <div className="w-4 h-4">
                      <TrashIcon />
                    </div>
                  </Button>
                </div>
              ),
            },
          ]}
          enableSearch
          searchPlaceholder={t("search_events", "Search events...")}
          onRowClick={handleRowClick}
        />
      </PageContent>

      {/* Create Event Modal */}
      <Modal
        open={showCreateModal}
        onClose={() => {
          setShowCreateModal(false);
          resetForm();
        }}
        title={t("create_event", "Create Event")}
        size="large"
      >
        <EventForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleCreateEvent}
          onCancel={() => {
            setShowCreateModal(false);
            resetForm();
          }}
          submitting={submitting}
          isEdit={false}
        />
      </Modal>

      {/* Edit Event Modal */}
      <Modal
        open={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setEditingEvent(null);
          resetForm();
        }}
        title={t("edit_event", "Edit Event")}
        size="large"
      >
        <EventForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleEditEvent}
          onCancel={() => {
            setShowEditModal(false);
            setEditingEvent(null);
            resetForm();
          }}
          submitting={submitting}
          isEdit={true}
        />
      </Modal>
    </>
  );
};

// Event Form Component
interface EventFormProps {
  formData: EventFormData;
  setFormData: React.Dispatch<React.SetStateAction<EventFormData>>;
  onSubmit: () => void;
  onCancel: () => void;
  submitting: boolean;
  isEdit: boolean;
}

const EventForm: React.FC<EventFormProps> = ({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  submitting,
  isEdit,
}) => {
  const { t } = useTranslation();
  const [categoryInput, setCategoryInput] = useState<string>("");

  useEffect(() => {
    setCategoryInput(formData.category?.join(", ") || "");
  }, []);

  const handleInputChange = (field: keyof EventFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCategoryChange = (value: string) => {
    setCategoryInput(value);
  };

  const handleCategoryBlur = () => {
    const categories = categoryInput
      .split(",")
      .map((cat) => cat.trim())
      .filter(Boolean);
    handleInputChange("category", categories);
  };

  return (
    <Card>
      <div className="card-content">
        <div className="form-fields">
          {/* Basic Information Section */}
          <div className="form-section">
            <h3>{t("basic_information", "Basic Information")}</h3>
            <div className="form-section-content">
              <TextInput
                label={t("event_name", "Event Name")}
                name="event_name"
                value={formData.event_name}
                onChange={(value) => handleInputChange("event_name", value)}
                placeholder={t("enter_event_name", "Enter event name")}
                required
              />

              <div className="form-row">
                <TextInput
                  label={t("host", "Host")}
                  name="host"
                  value={formData.host}
                  onChange={(value) => handleInputChange("host", value)}
                  placeholder={t("enter_host_name", "Enter host name")}
                />
                <TextInput
                  label={t("starting_price", "Starting Price")}
                  name="starting_price"
                  value={formData.starting_price}
                  onChange={(value) =>
                    handleInputChange("starting_price", value)
                  }
                  placeholder={t("enter_price", "e.g., $25, Free, TBD")}
                />
              </div>

              <TextInput
                label={t("categories", "Categories")}
                name="category"
                value={categoryInput}
                onChange={handleCategoryChange}
                onBlur={handleCategoryBlur}
                placeholder={t(
                  "enter_categories",
                  "Enter categories separated by commas"
                )}
                subtitle={t(
                  "category_help",
                  "e.g., Music, Food, Entertainment"
                )}
              />
            </div>
          </div>

          {/* Date & Time Section */}
          <div className="form-section">
            <h3>{t("date_time", "Date & Time")}</h3>
            <div className="form-section-content">
              <div className="form-row">
                <TextInput
                  label={t("start_time", "Start Time")}
                  name="start_time"
                  type="datetime-local"
                  value={formData.start_time}
                  onChange={(value) => handleInputChange("start_time", value)}
                  min={new Date().toISOString().slice(0, 16)}
                  required
                />
                <TextInput
                  label={t("timezone", "Timezone")}
                  name="timezone"
                  value={formData.timezone}
                  onChange={(value) => handleInputChange("timezone", value)}
                  placeholder={t("enter_timezone", "e.g., America/New_York")}
                />
              </div>
            </div>
          </div>

          {/* Location Section */}
          <div className="form-section">
            <h3>{t("location", "Location")}</h3>
            <div className="form-section-content">
              <TextInput
                label={t("address", "Address")}
                name="address"
                value={formData.address}
                onChange={(value) => handleInputChange("address", value)}
                placeholder={t("enter_address", "Enter event address")}
              />

              <div className="form-row">
                <TextInput
                  label={t("city", "City")}
                  name="city"
                  value={formData.city}
                  onChange={(value) => handleInputChange("city", value)}
                  placeholder={t("enter_city", "Enter city")}
                />
                <TextInput
                  label={t("state", "State")}
                  name="state"
                  value={formData.state}
                  onChange={(value) => handleInputChange("state", value)}
                  placeholder={t("enter_state", "Enter state")}
                />
                <TextInput
                  label={t("postal_code", "Postal Code")}
                  name="postal_code"
                  value={formData.postal_code}
                  onChange={(value) => handleInputChange("postal_code", value)}
                  placeholder={t("enter_postal_code", "Enter postal code")}
                />
              </div>
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="form-section">
            <h3>{t("additional_information", "Additional Information")}</h3>
            <div className="form-section-content">
              <TextInput
                label={t("image_url", "Image URL")}
                name="image"
                type="text"
                value={formData.image}
                onChange={(value) => handleInputChange("image", value)}
                placeholder={t("enter_image_url", "Enter image URL")}
              />

              <TextInput
                label={t("event_url", "Event URL")}
                name="url"
                type="text"
                value={formData.url}
                onChange={(value) => handleInputChange("url", value)}
                placeholder={t("enter_event_url", "Enter event URL")}
              />
            </div>
          </div>
        </div>

        <div className="form-actions">
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={onCancel}
              disabled={submitting}
            >
              {t("cancel", "Cancel")}
            </Button>
            <Button
              onClick={onSubmit}
              disabled={
                submitting ||
                !formData.event_name.trim() ||
                !formData.start_time.trim()
              }
            >
              {submitting ? (
                <>
                  <Spinner size="small" className="mr-2" />
                  {isEdit
                    ? t("updating", "Updating...")
                    : t("creating", "Creating...")}
                </>
              ) : isEdit ? (
                t("update_event", "Update Event")
              ) : (
                t("create_event", "Create Event")
              )}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default Events;
